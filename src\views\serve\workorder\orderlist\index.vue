<!--
 * @Description: 
 * @Version: 1.0
 * @Autor: wjb
 * @Date: 2025-07-10 08:57:30
 * @LastEditors: wjb
 * @LastEditTime: 2025-10-20 09:03:34
-->
<template>
  <div class="container">
    <div class="card">
      <div class="flex-c">
        <div class="cardTitle">工单列表</div>
      </div>
      <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
        <el-tab-pane label="应用工单" name="first"></el-tab-pane>
        <el-tab-pane label="项目工单" name="second"></el-tab-pane>
      </el-tabs>
      <applicationOrder v-if="activeName === 'first'"></applicationOrder>
      <projectOrder v-if="activeName === 'second'"></projectOrder>
    </div>
  </div>
</template>

<script>
import projectOrder from "./projectOrderList.vue";
import applicationOrder from "./applicationOrderList.vue";
export default {
  components: { applicationOrder, projectOrder },
  dicts: [],
  data() {
    return {
      activeName: "first",
    };
  },
  created() {},
  methods: {
    handleClick() {},
  },
};
</script>

<style lang="scss" scoped>
.card {
  border-radius: 15px;
  padding: 20px 20px;
  box-sizing: border-box;
  background-color: #fff;
  height: auto;
  margin-bottom: 12px;
  .cardTitle {
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 700;
    font-size: 18px;
    color: #1d2129;
    line-height: 24px;
    text-align: left;
    margin-bottom: 10px;
    padding-left: 18px;
    box-sizing: border-box;
    position: relative;
    &::before {
      content: "";
      position: absolute;
      top: 2px;
      left: 0;
      width: 10px;
      height: 20px;
      background: url("~@/assets/images/cardTitle_icon.png");
      background-size: 100% 100%;
    }
  }
}
</style>